package com.example.shorts.ui.node.screen.home

import android.os.Parcelable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import com.example.shorts.foundation.android.findActivity
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.foundation.in_app_message.InAppMessageManager
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.node.screen.home.discover.DiscoverScreen
import com.example.shorts.ui.node.screen.home.shorts.ShortsScreen
import com.example.shorts.ui.node.screen.home.shorts.model.ShortsUiModel
import com.example.shorts.ui.node.screen.home.tasks.CheckInUiModel
import com.example.shorts.ui.node.screen.home.tasks.TasksScreen
import com.example.shorts.ui.node.screen.home.tasks.TasksUiModel
import com.roudikk.guia.core.Navigator
import dev.chrisbanes.haze.HazeDefaults
import dev.chrisbanes.haze.HazeProgressive
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import kotlin.time.Duration.Companion.minutes


sealed interface HomeTab : Parcelable {
  @Parcelize
  data object Discover : HomeTab

  @Parcelize
  data object Shorts : HomeTab

  @Parcelize
  data object Tasks : HomeTab

  @Parcelize
  data object Games : HomeTab
}

@Parcelize
class HomeNode(
  private val initialTab: HomeTab = HomeTab.Shorts
) : ScreenNode("home") {
  @Composable
  override fun Content(navigator: Navigator) {
    val context = LocalContext.current
    val activity = remember(context) { context.findActivity() }
    val viewModel: HomeUiModel = koinUiModel { parametersOf(initialTab) }
    val uiState by viewModel.collectAsState()

    LaunchedEffect(uiState.currentTab) {
      InAppMessageManager.showPaypalWithdrawMessageIfCooldownPassed(
        activity = activity,
        cooldownDuration = 1.minutes
      )
    }

    HomeContent(
      navigator = navigator,
      uiState = uiState,
      onTabSelected = viewModel::selectTab
    )
  }
}

@Composable
private fun HomeContent(
  navigator: Navigator,
  uiState: HomeUiState,
  onTabSelected: (HomeTab) -> Unit
) {
  val hazeState = rememberHazeState()
  val density = LocalDensity.current
  val statusBarHeight = with(density) {
    WindowInsets.statusBars.asPaddingValues().calculateTopPadding()
  }

  val tasksUiModel: TasksUiModel = koinUiModel()
  val checkInUiModel: CheckInUiModel = koinUiModel()
  val shortsUiModel: ShortsUiModel = koinInject()

  LifecycleEventEffect(event = Lifecycle.Event.ON_START) {
    tasksUiModel.onLoad()
    checkInUiModel.onLoad()
  }

  Scaffold(
    topBar = {
      TopAppBar(
        title = {},
        modifier = Modifier
          .height(statusBarHeight)
          .hazeEffect(state = hazeState),
        colors = TopAppBarDefaults.topAppBarColors(
          containerColor = Color.Transparent,
          titleContentColor = Color.White,
          navigationIconContentColor = Color.White,
          actionIconContentColor = Color.White
        ),
      )
    },
    bottomBar = {
      HomeBottomBar(
        selectedTab = uiState.currentTab,
        onTabSelected = onTabSelected,
        modifier = Modifier.hazeEffect(
          state = hazeState,
          style = HazeDefaults.style(backgroundColor = MaterialTheme.colorScheme.surface),
        )
      )
    }
  ) { paddingValues ->
    when (uiState.currentTab) {
      HomeTab.Discover -> DiscoverScreen(
        modifier = Modifier
          .fillMaxSize()
          .padding(paddingValues)
          .verticalScroll(rememberScrollState())
          .hazeSource(state = hazeState)
      )

      HomeTab.Shorts -> ShortsScreen(
        modifier = Modifier
          .fillMaxSize()
          .padding(paddingValues)
          .hazeSource(state = hazeState),
        uiModel = shortsUiModel
      )
      HomeTab.Games -> {}
      HomeTab.Tasks -> TasksScreen(
        navigator = navigator,
        tasksUiModel = tasksUiModel,
        checkInUiModel = checkInUiModel,
        modifier = Modifier
          .fillMaxSize()
          .padding(paddingValues)
          .verticalScroll(rememberScrollState())
          .hazeSource(state = hazeState)
      )
    }
  }
}
