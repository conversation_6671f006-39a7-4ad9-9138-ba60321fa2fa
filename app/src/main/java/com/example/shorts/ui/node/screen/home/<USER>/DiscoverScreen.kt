package com.example.shorts.ui.node.screen.home.discover

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.ui.common.withdraw.WithdrawBar
import com.example.shorts.ui.node.screen.home.shorts.DramaPlayScreenNode
import com.example.shorts.ui.node.screen.wallet.WalletNode
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import kotlinx.coroutines.delay

@Composable
fun DiscoverScreen(
  modifier: Modifier = Modifier
) {
  val navigator = requireLocalNavigator()
  val context = LocalContext.current

  // State for different sections
  var recommendedDramas by remember { mutableStateOf<List<ShortPlay>>(emptyList()) }
  var newReleases by remember { mutableStateOf<List<ShortPlay>>(emptyList()) }
  var categories by remember { mutableStateOf<List<ShortPlay.ShortPlayCategory>>(emptyList()) }
  var selectedCategoryId by remember { mutableStateOf<Long?>(null) }
  var categoryDramas by remember { mutableStateOf<List<ShortPlay>>(emptyList()) }
  var isLoading by remember { mutableStateOf(true) }

  // Load initial data
  LaunchedEffect(Unit) {
    // Wait for SDK initialization before making API calls
    while (!PSSDK.hasInitialized()) {
      delay(100)
    }

    loadInitialData(
      onRecommendedLoaded = { recommendedDramas = it.take(3) },
      onNewReleasesLoaded = { newReleases = it.take(3) },
      onCategoriesLoaded = {
        categories = it
        // Select first category by default
        if (it.isNotEmpty()) {
          selectedCategoryId = it.first().id
        }
      },
      onLoadingComplete = { isLoading = false }
    )
  }

  // Load category dramas when selected category changes
  LaunchedEffect(selectedCategoryId) {
    selectedCategoryId?.let { categoryId ->
      loadCategoryDramas(categoryId) { dramas ->
        categoryDramas = dramas
      }
    }
  }

  Column(modifier) {
    WithdrawBar(
      onWithdrawClick = { navigator.push(WalletNode()) },
      modifier = Modifier.padding(16.dp)
    )

    if (isLoading) {
      Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
      ) {
        CircularProgressIndicator()
      }
    } else {
      LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
      ) {
        // Recommended for you section
        item {
          RecommendedSection(
            dramas = recommendedDramas,
            onDramaClick = { drama ->
              navigator.push(DramaPlayScreenNode(drama))
            }
          )
        }

        // New Releases section
        item {
          NewReleasesSection(
            dramas = newReleases,
            onDramaClick = { drama ->
              navigator.push(DramaPlayScreenNode(drama))
            }
          )
        }

        // Categories section
        item {
          CategoriesSection(
            categories = categories,
            selectedCategoryId = selectedCategoryId,
            onCategorySelected = { categoryId ->
              selectedCategoryId = categoryId
            }
          )
        }

        // Category dramas grid
        item {
          CategoryDramasSection(
            dramas = categoryDramas,
            onDramaClick = { drama ->
              navigator.push(DramaPlayScreenNode(drama))
            }
          )
        }
      }
    }
  }
}

// Helper functions for loading data
private fun loadInitialData(
  onRecommendedLoaded: (List<ShortPlay>) -> Unit,
  onNewReleasesLoaded: (List<ShortPlay>) -> Unit,
  onCategoriesLoaded: (List<ShortPlay.ShortPlayCategory>) -> Unit,
  onLoadingComplete: () -> Unit
) {
  var loadedCount = 0
  val totalLoads = 3

  fun checkComplete() {
    loadedCount++
    if (loadedCount >= totalLoads) {
      onLoadingComplete()
    }
  }

  // Load recommended dramas (popular)
  PSSDK.requestPopularDrama(1, 3, object : PSSDK.FeedListResultListener {
    override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
      onRecommendedLoaded(result.dataList)
      checkComplete()
    }

    override fun onFail(errorInfo: PSSDK.ErrorInfo) {
      onRecommendedLoaded(emptyList())
      checkComplete()
    }
  })

  // Load new releases
  PSSDK.requestNewDrama(1, 3, object : PSSDK.FeedListResultListener {
    override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
      onNewReleasesLoaded(result.dataList)
      checkComplete()
    }

    override fun onFail(errorInfo: PSSDK.ErrorInfo) {
      onNewReleasesLoaded(emptyList())
      checkComplete()
    }
  })

  // Load categories
  val contentLanguages = PSSDK.getContentLanguages()
  val language = if (contentLanguages?.isNotEmpty() == true) contentLanguages[0] else ""

  PSSDK.requestCategoryList(language, object : PSSDK.CategoryListResultListener {
    override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay.ShortPlayCategory>) {
      // Filter categories that have content
      val validCategories = result.dataList.filter { category ->
        category.count > 0 && !category.name.isNullOrEmpty()
      }
      onCategoriesLoaded(validCategories)
      checkComplete()
    }

    override fun onFail(errorInfo: PSSDK.ErrorInfo) {
      onCategoriesLoaded(emptyList())
      checkComplete()
    }
  })
}

private fun loadCategoryDramas(
  categoryId: Long,
  onDramasLoaded: (List<ShortPlay>) -> Unit
) {
  val categoryIds = listOf(categoryId)
  PSSDK.requestFeedListByCategoryIds(categoryIds, null, 1, 20, object : PSSDK.FeedListResultListener {
    override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
      onDramasLoaded(result.dataList)
    }

    override fun onFail(errorInfo: PSSDK.ErrorInfo) {
      onDramasLoaded(emptyList())
    }
  })
}

// Composable sections
@Composable
private fun RecommendedSection(
  dramas: List<ShortPlay>,
  onDramaClick: (ShortPlay) -> Unit
) {
  Column {
    Text(
      text = "Recommended for you",
      fontSize = 18.sp,
      fontWeight = FontWeight.Bold,
      color = Color.Black,
      modifier = Modifier.padding(bottom = 12.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
      items(dramas) { drama ->
        DramaCard(
          drama = drama,
          onClick = { onDramaClick(drama) }
        )
      }
    }
  }
}

@Composable
private fun NewReleasesSection(
  dramas: List<ShortPlay>,
  onDramaClick: (ShortPlay) -> Unit
) {
  Column {
    Text(
      text = "New Releases",
      fontSize = 18.sp,
      fontWeight = FontWeight.Bold,
      color = Color.Black,
      modifier = Modifier.padding(bottom = 12.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
      items(dramas) { drama ->
        DramaCard(
          drama = drama,
          onClick = { onDramaClick(drama) }
        )
      }
    }
  }
}

@Composable
private fun CategoriesSection(
  categories: List<ShortPlay.ShortPlayCategory>,
  selectedCategoryId: Long?,
  onCategorySelected: (Long) -> Unit
) {
  Column {
    Text(
      text = "Categories",
      fontSize = 18.sp,
      fontWeight = FontWeight.Bold,
      color = Color.Black,
      modifier = Modifier.padding(bottom = 12.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
      items(categories) { category ->
        CategoryChip(
          category = category,
          isSelected = category.id == selectedCategoryId,
          onClick = { onCategorySelected(category.id) }
        )
      }
    }
  }
}

@Composable
private fun CategoryDramasSection(
  dramas: List<ShortPlay>,
  onDramaClick: (ShortPlay) -> Unit
) {
  if (dramas.isNotEmpty()) {
    LazyVerticalGrid(
      columns = GridCells.Fixed(3),
      horizontalArrangement = Arrangement.spacedBy(8.dp),
      verticalArrangement = Arrangement.spacedBy(12.dp),
      modifier = Modifier.height(400.dp) // Fixed height to prevent infinite height issues
    ) {
      items(dramas) { drama ->
        DramaGridItem(
          drama = drama,
          onClick = { onDramaClick(drama) }
        )
      }
    }
  }
}

@Composable
private fun DramaCard(
  drama: ShortPlay,
  onClick: () -> Unit
) {
  Card(
    modifier = Modifier
      .width(120.dp)
      .clickable { onClick() },
    shape = RoundedCornerShape(8.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
  ) {
    Column {
      AsyncImage(
        model = drama.coverImage,
        contentDescription = drama.title,
        modifier = Modifier
          .fillMaxWidth()
          .height(160.dp),
        contentScale = ContentScale.Crop
      )

      Column(
        modifier = Modifier.padding(8.dp)
      ) {
        Text(
          text = drama.title,
          fontSize = 12.sp,
          fontWeight = FontWeight.Medium,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
          color = Color.Black
        )

        Text(
          text = "${drama.total} Episodes",
          fontSize = 10.sp,
          color = Color.Gray,
          modifier = Modifier.padding(top = 4.dp)
        )
      }
    }
  }
}

@Composable
private fun DramaGridItem(
  drama: ShortPlay,
  onClick: () -> Unit
) {
  Card(
    modifier = Modifier
      .fillMaxWidth()
      .clickable { onClick() },
    shape = RoundedCornerShape(8.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
  ) {
    Column {
      AsyncImage(
        model = drama.coverImage,
        contentDescription = drama.title,
        modifier = Modifier
          .fillMaxWidth()
          .height(120.dp),
        contentScale = ContentScale.Crop
      )

      Column(
        modifier = Modifier.padding(6.dp)
      ) {
        Text(
          text = drama.title,
          fontSize = 11.sp,
          fontWeight = FontWeight.Medium,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
          color = Color.Black
        )

        Text(
          text = "${drama.total}集",
          fontSize = 9.sp,
          color = Color.Gray,
          modifier = Modifier.padding(top = 2.dp)
        )
      }
    }
  }
}

@Composable
private fun CategoryChip(
  category: ShortPlay.ShortPlayCategory,
  isSelected: Boolean,
  onClick: () -> Unit
) {
  val backgroundColor = if (isSelected) {
    MaterialTheme.colorScheme.primary
  } else {
    Color.LightGray
  }

  val textColor = if (isSelected) {
    Color.White
  } else {
    Color.Black
  }

  Surface(
    modifier = Modifier
      .clickable { onClick() }
      .clip(RoundedCornerShape(16.dp)),
    color = backgroundColor,
    shape = RoundedCornerShape(16.dp)
  ) {
    Text(
      text = category.name,
      modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
      fontSize = 12.sp,
      color = textColor,
      fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
    )
  }
}