package com.example.shorts.ui.node.screen.home.discover

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.common.withdraw.WithdrawBar
import com.example.shorts.ui.node.screen.home.shorts.DramaPlayScreenNode
import com.example.shorts.ui.node.screen.wallet.WalletNode
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun DiscoverScreen(
  modifier: Modifier = Modifier
) {
  val navigator = requireLocalNavigator()
  val uiModel: DiscoverUiModel = koinUiModel()
  val uiState by uiModel.collectAsState()

  DiscoverContent(
    uiState = uiState,
    onCategorySelected = uiModel::onCategorySelected,
    onDramaClick = { drama ->
      navigator.push(DramaPlayScreenNode(drama))
    },
    onWithdrawClick = {
      navigator.push(WalletNode())
    },
    modifier = modifier
  )
}

@Composable
private fun DiscoverContent(
  uiState: DiscoverUiState,
  onCategorySelected: (Long) -> Unit,
  onDramaClick: (ShortPlay) -> Unit,
  onWithdrawClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  when {
    uiState.isLoading && uiState.recommendedDramas.isEmpty() -> {
      Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
      ) {
        CircularProgressIndicator()
      }
    }

    uiState.error != null && uiState.recommendedDramas.isEmpty() -> {
      Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
      ) {
        Text(
          text = uiState.error,
          style = MaterialTheme.typography.bodyLarge,
          color = MaterialTheme.colorScheme.error
        )
      }
    }

    else -> {
      // Use LazyColumn instead of Column with verticalScroll to fix the scrolling issue
      LazyColumn(modifier = modifier) {
        // Withdraw bar at the top
        item {
          WithdrawBar(
            onWithdrawClick = onWithdrawClick,
            modifier = Modifier.padding(top = 16.dp).padding(horizontal = 16.dp)
          )
        }

        // Recommended for you section
        item {
          RecommendedSection(
            dramas = uiState.recommendedDramas,
            onDramaClick = onDramaClick,
            modifier = Modifier.padding(horizontal = 16.dp)
          )
        }

        // New Releases section
        item {
          NewReleasesSection(
            dramas = uiState.newReleases,
            onDramaClick = onDramaClick,
            modifier = Modifier.padding(horizontal = 16.dp)
          )
        }

        // Categories section
        item {
          CategoriesSection(
            categories = uiState.categories,
            selectedCategoryId = uiState.selectedCategoryId,
            onCategorySelected = onCategorySelected,
            isLoading = uiState.isLoadingCategories,
            modifier = Modifier.padding(horizontal = 16.dp)
          )
        }

        // Category dramas grid
        item {
          CategoryDramasSection(
            dramas = uiState.categoryDramas,
            onDramaClick = onDramaClick,
            isLoading = uiState.isLoadingCategoryDramas,
            modifier = Modifier.padding(horizontal = 16.dp)
          )
        }

        // Bottom spacing
        item {
          Spacer(modifier = Modifier.height(16.dp))
        }
      }
    }
  }
}

// UI Components

// Composable sections
@Composable
private fun RecommendedSection(
  dramas: List<ShortPlay>,
  onDramaClick: (ShortPlay) -> Unit,
  modifier: Modifier = Modifier
) {
  Column(modifier) {
    Text(
      text = "Recommended for you",
      fontSize = 18.sp,
      fontWeight = FontWeight.Bold,
      color = Color.Black,
      modifier = Modifier.padding(bottom = 12.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
      items(dramas) { drama ->
        DramaCard(
          drama = drama,
          onClick = { onDramaClick(drama) }
        )
      }
    }
  }
}

@Composable
private fun NewReleasesSection(
  dramas: List<ShortPlay>,
  onDramaClick: (ShortPlay) -> Unit,
  modifier: Modifier = Modifier
) {
  Column(modifier) {
    Text(
      text = "New Releases",
      fontSize = 18.sp,
      fontWeight = FontWeight.Bold,
      color = Color.Black,
      modifier = Modifier.padding(bottom = 12.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
      items(dramas) { drama ->
        DramaCard(
          drama = drama,
          onClick = { onDramaClick(drama) }
        )
      }
    }
  }
}

@Composable
private fun CategoriesSection(
  categories: List<ShortPlay.ShortPlayCategory>,
  selectedCategoryId: Long?,
  onCategorySelected: (Long) -> Unit,
  isLoading: Boolean = false,
  modifier: Modifier = Modifier
) {
  Column(modifier) {
    Text(
      text = "Categories",
      fontSize = 18.sp,
      fontWeight = FontWeight.Bold,
      color = Color.Black,
      modifier = Modifier.padding(bottom = 12.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
      items(categories) { category ->
        CategoryChip(
          category = category,
          isSelected = category.id == selectedCategoryId,
          onClick = { onCategorySelected(category.id) }
        )
      }
    }
  }
}

@Composable
private fun CategoryDramasSection(
  dramas: List<ShortPlay>,
  onDramaClick: (ShortPlay) -> Unit,
  isLoading: Boolean = false,
  modifier: Modifier = Modifier
) {
  Column(modifier) {
    if (isLoading) {
      Box(
        modifier = Modifier
          .fillMaxWidth()
          .height(200.dp),
        contentAlignment = Alignment.Center
      ) {
        CircularProgressIndicator()
      }
    } else if (dramas.isNotEmpty()) {
      LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier.height(400.dp) // Fixed height to prevent infinite height issues
      ) {
        items(dramas) { drama ->
          DramaGridItem(
            drama = drama,
            onClick = { onDramaClick(drama) }
          )
        }
      }
    }
  }
}

@Composable
private fun DramaCard(
  drama: ShortPlay,
  onClick: () -> Unit
) {
  Card(
    modifier = Modifier
      .width(120.dp)
      .clickable { onClick() },
    shape = RoundedCornerShape(8.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
  ) {
    Column {
      AsyncImage(
        model = drama.coverImage,
        contentDescription = drama.title,
        modifier = Modifier
          .fillMaxWidth()
          .height(160.dp),
        contentScale = ContentScale.Crop
      )

      Column(
        modifier = Modifier.padding(8.dp)
      ) {
        Text(
          text = drama.title,
          fontSize = 12.sp,
          fontWeight = FontWeight.Medium,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
          color = Color.Black
        )

        Text(
          text = "${drama.total} Episodes",
          fontSize = 10.sp,
          color = Color.Gray,
          modifier = Modifier.padding(top = 4.dp)
        )
      }
    }
  }
}

@Composable
private fun DramaGridItem(
  drama: ShortPlay,
  onClick: () -> Unit
) {
  Card(
    modifier = Modifier
      .fillMaxWidth()
      .clickable { onClick() },
    shape = RoundedCornerShape(8.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
  ) {
    Column {
      AsyncImage(
        model = drama.coverImage,
        contentDescription = drama.title,
        modifier = Modifier
          .fillMaxWidth()
          .height(120.dp),
        contentScale = ContentScale.Crop
      )

      Column(
        modifier = Modifier.padding(6.dp)
      ) {
        Text(
          text = drama.title,
          fontSize = 11.sp,
          fontWeight = FontWeight.Medium,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
          color = Color.Black
        )

        Text(
          text = "${drama.total}集",
          fontSize = 9.sp,
          color = Color.Gray,
          modifier = Modifier.padding(top = 2.dp)
        )
      }
    }
  }
}

@Composable
private fun CategoryChip(
  category: ShortPlay.ShortPlayCategory,
  isSelected: Boolean,
  onClick: () -> Unit
) {
  val backgroundColor = if (isSelected) {
    MaterialTheme.colorScheme.primary
  } else {
    Color.LightGray
  }

  val textColor = if (isSelected) {
    Color.White
  } else {
    Color.Black
  }

  Surface(
    modifier = Modifier
      .clickable { onClick() }
      .clip(RoundedCornerShape(16.dp)),
    color = backgroundColor,
    shape = RoundedCornerShape(16.dp)
  ) {
    Text(
      text = category.name,
      modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
      fontSize = 12.sp,
      color = textColor,
      fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
    )
  }
}