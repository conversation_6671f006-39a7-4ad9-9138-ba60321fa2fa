package com.example.shorts.ui.node.screen.home.discover

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.shorts.ui.common.withdraw.WithdrawBar
import com.example.shorts.ui.node.screen.wallet.WalletNode
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator

@Composable
fun DiscoverScreen(
  modifier: Modifier = Modifier
) {
  val navigator = requireLocalNavigator()

  Column(modifier) {
    WithdrawBar(
      onWithdrawClick = { navigator.push(WalletNode()) },
      modifier = Modifier.padding(16.dp)
    )

  }
}