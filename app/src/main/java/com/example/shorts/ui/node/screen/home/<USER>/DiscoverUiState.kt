package com.example.shorts.ui.node.screen.home.discover

import com.bytedance.sdk.shortplay.api.ShortPlay

data class DiscoverUiState(
  val recommendedDramas: List<ShortPlay> = emptyList(),
  val newReleases: List<ShortPlay> = emptyList(),
  val categories: List<ShortPlay.ShortPlayCategory> = emptyList(),
  val selectedCategoryId: Long? = null,
  val categoryDramas: List<ShortPlay> = emptyList(),
  val isLoading: Boolean = true,
  val isLoadingCategories: Boolean = false,
  val isLoadingCategoryDramas: Boolean = false,
  val error: String? = null
)
